let cachedDashboardData = null;
let cachedTableData = null;
let dashboardDataLoaded = false;
let tableDataLoaded = false;

async function updateDashboardData(userCompany) {
    try {
        // Ensure we're using the correct company ID
        const currentCompany = userCompany || (window.isDemoMode ? 'Barefoot eLearning' : window.originalUserCompany);

        // Use different cache keys for demo vs live mode to avoid conflicts
        const cachePrefix = window.isDemoMode ? 'demo_' : '';
        const cachedData = sessionStorage.getItem(`${cachePrefix}dashboardData`);
        const lastFetchTime = sessionStorage.getItem(`${cachePrefix}dashboardLastFetchTime`);
        const currentTime = Date.now();

        // First hide all content and show skeletons
        document.querySelectorAll('.card-content').forEach(content => content.style.display = 'none');
        document.querySelectorAll('.card-empty').forEach(empty => empty.style.display = 'none');
        document.querySelectorAll('.skeleton-card').forEach(card => card.style.display = 'block');

        if (cachedData && lastFetchTime && (currentTime - lastFetchTime < 300000)) {
            cachedDashboardData = JSON.parse(cachedData);
            updateDashboardUI(cachedDashboardData);
            dashboardDataLoaded = true;
            checkAllDataLoaded();
            return cachedDashboardData;
        }

        loadingOverlay.style.display = 'flex';

        document.querySelectorAll('.skeleton-row').forEach(row => row.style.display = 'table-row');
        document.querySelectorAll('.skeleton-card').forEach(card => card.style.display = 'block');
        document.querySelectorAll('.card-content').forEach(content => content.style.display = 'none');
        document.querySelectorAll('.card-empty').forEach(empty => empty.style.display = 'none');

        const companyRef = db.collection('companies').doc(currentCompany);
        const userSnapshot = await companyRef.collection('users').get();
        const users = userSnapshot.docs;

        const employeeData = {
            essentials: { employees: 0, percentage: 0 },
            intermediate: { employees: 0, percentage: 0 },
            advanced: { employees: 0, percentage: 0 },
            champions: { employees: 0, percentage: 0 }
        };

        // Helper function to determine level priority
        const getLevelPriority = (level) => {
            const priorities = {
                'champions': 4,
                'advanced': 3,
                'intermediate': 2,
                'essentials': 1
            };
            return priorities[level?.toLowerCase()] || 0;
        };

        const userPromises = users.map(async (doc) => {
            // Fetch both assessment types simultaneously
            const [digitalResultsSnapshot, softSkillsResultsSnapshot] = await Promise.all([
                doc.ref.collection('assessmentResults')
                    .orderBy('timestamp', 'desc')
                    .limit(1)
                    .get(),
                doc.ref.collection('softSkillsAssessmentResults')
                    .orderBy('timestamp', 'desc')
                    .limit(1)
                    .get()
            ]);

            // Get the latest results from both assessments
            const digitalResult = !digitalResultsSnapshot.empty ? digitalResultsSnapshot.docs[0].data() : null;
            const softSkillsResult = !softSkillsResultsSnapshot.empty ? softSkillsResultsSnapshot.docs[0].data() : null;

            // Get learning paths from both assessments
            const digitalPath = digitalResult?.section?.toLowerCase();
            const softSkillsPath = softSkillsResult?.section?.toLowerCase();

            // Only increment once per user for the matching category
            if ((digitalPath && employeeData[digitalPath]) ||
                (softSkillsPath && employeeData[softSkillsPath])) {
                employeeData[digitalPath || softSkillsPath].employees++;
            }
        });

        await Promise.all(userPromises);

        const totalEmployees = Object.values(employeeData).reduce((total, { employees }) => total + employees, 0) || 1;
        Object.keys(employeeData).forEach(key => {
            employeeData[key].percentage = (employeeData[key].employees / totalEmployees) * 100;
        });

        cachedDashboardData = employeeData;
        sessionStorage.setItem(`${cachePrefix}dashboardData`, JSON.stringify(cachedDashboardData));
        sessionStorage.setItem(`${cachePrefix}dashboardLastFetchTime`, currentTime.toString());

        updateDashboardUI(cachedDashboardData);
        dashboardDataLoaded = true;
        checkAllDataLoaded();
        return cachedDashboardData;

    } catch (error) {
        console.error('Error updating dashboard data:', error);
        dashboardDataLoaded = true;
        checkAllDataLoaded();
        return null;
    }
}

function updateDashboardUI(data) {
    const cards = document.querySelectorAll('.card[data-path]');
    cards.forEach(card => {
        const category = card.getAttribute('data-path');
        const employeeCount = data[category].employees;
        const percentage = data[category].percentage;

        // Always hide skeleton first
        const skeletonCard = card.querySelector('.skeleton-card');
        const cardContent = card.querySelector('.card-content');
        const cardEmpty = card.querySelector('.card-empty');

        if (skeletonCard) skeletonCard.style.display = 'none';

        if (employeeCount === 0) {
            if (cardContent) cardContent.style.display = 'none';
            if (cardEmpty) cardEmpty.style.display = 'block';
            card.classList.add('empty');
        } else {
            if (cardContent) cardContent.style.display = 'block';
            if (cardEmpty) cardEmpty.style.display = 'none';
            card.classList.remove('empty');

            const countElement = card.querySelector('.text-4xl');
            const percentElement = card.querySelector('span');

            if (countElement) countElement.textContent = employeeCount;
            if (percentElement) percentElement.textContent = `${percentage.toFixed(0)}%`;
        }
    });

    const recentAssessmentsCard = document.querySelector('.mt-8 .card');
    if (recentAssessmentsCard) {
        const skeletonCard = recentAssessmentsCard.querySelector('.skeleton-card');
        const cardContent = recentAssessmentsCard.querySelector('.card-content');

        if (skeletonCard) skeletonCard.style.display = 'none';
        if (cardContent) cardContent.style.display = 'block';
    }
}

// Helper functions for assessment display - Add these before initializeTable
function getScoreDisplay(result) {
    if (!result) return 'pending';
    if (!result.totalQuestions) return '-------';
    return `${((result.score / result.totalQuestions) * 100).toFixed(0)}%`;
}

function getPathDisplay(result, type = 'digital') {
    if (!result) return 'pending';
    return result.section || '-------';
}

function generateScoreDisplay(assessment, type) {
    if (assessment.status === 'pending') return '';
    const scoreColor = getScoreColor(assessment.score);
    return `
        <span class="${scoreColor} px-2 py-1 rounded-full text-sm">
            ${type}: ${assessment.score}
        </span>
    `;
}

function generatePathDisplay(assessment, type) {
    if (assessment.status === 'pending') return '';
    return `
        <span class="text-sm">
            ${type}: ${assessment.learningPath}
        </span>
    `;
}

function getScoreColor(score) {
    if (score === 'pending' || score === '-------') {
        return 'bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400';
    }
    const numScore = parseFloat(score);
    if (isNaN(numScore)) return '';
    if (numScore < 30) return 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400';
    if (numScore < 60) return 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400';
    return 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400';
}

function formatDate(date) {
    if (!date) return '';
    return date instanceof Date ? date.toISOString().split('T')[0] : new Date(date).toISOString().split('T')[0];
}

async function initializeTable(userCompany) {
    try {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) loadingOverlay.style.display = 'flex';

        // Ensure we're using the correct company ID
        const currentCompany = userCompany || (window.isDemoMode ? 'Barefoot eLearning' : window.originalUserCompany);

        // Use different cache keys for demo vs live mode
        const cachePrefix = window.isDemoMode ? 'demo_' : '';

        // Show skeleton rows and hide actual table header
        document.querySelectorAll('.skeleton-row').forEach(row => row.style.display = 'table-row');
        const tableHeader = document.querySelector('.table-header');
        if (tableHeader) tableHeader.style.display = 'none';

        const cachedData = sessionStorage.getItem(`${cachePrefix}tableData`);
        const lastFetchTime = sessionStorage.getItem(`${cachePrefix}tableLastFetchTime`);
        const currentTime = Date.now();

        if (cachedData && lastFetchTime && (currentTime - lastFetchTime < 300000)) {
            cachedTableData = JSON.parse(cachedData);
            // Convert createdAt strings back to Date objects
            cachedTableData.forEach(item => {
                if (item.createdAt) {
                    item.createdAt = new Date(item.createdAt);
                }
            });
            updateTableUI(cachedTableData);
            tableDataLoaded = true;
            checkAllDataLoaded();
            return cachedTableData;
        }

        const companyRef = db.collection('companies').doc(currentCompany);
        const userSnapshot = await companyRef.collection('users').get();
        const users = userSnapshot.docs;

        const assessments = [];

        const userPromises = users.map(async (doc) => {
            const userData = doc.data();
            const [digitalResultsSnapshot, digitalSummarySnapshot, softSkillsResultsSnapshot, softSkillsSummarySnapshot] = await Promise.all([
                doc.ref.collection('assessmentResults')
                    .orderBy('timestamp', 'desc')
                    .limit(1)
                    .get(),
                doc.ref.collection('assessmentSummaries')
                    .orderBy('timestamp', 'desc')
                    .limit(1)
                    .get(),
                doc.ref.collection('softSkillsAssessmentResults')
                    .orderBy('timestamp', 'desc')
                    .limit(1)
                    .get(),
                doc.ref.collection('softSkillsSummaries')
                    .orderBy('timestamp', 'desc')
                    .limit(1)
                    .get()
            ]);

            const digitalResult = !digitalResultsSnapshot.empty ? digitalResultsSnapshot.docs[0].data() : null;
            const digitalSummary = !digitalSummarySnapshot.empty ? digitalSummarySnapshot.docs[0].data() : null;
            const softSkillsResult = !softSkillsResultsSnapshot.empty ? softSkillsResultsSnapshot.docs[0].data() : null;
            const softSkillsSummary = !softSkillsSummarySnapshot.empty ? softSkillsSummarySnapshot.docs[0].data() : null;

            const createdAt = userData.createdAt ? userData.createdAt.toDate() : null;

            // Determine scores and paths for both assessment types
            const digitalScore = getScoreDisplay(digitalResult);
            const softSkillsScore = getScoreDisplay(softSkillsResult);
            const digitalPath = getPathDisplay(digitalResult);
            const softSkillsPath = getPathDisplay(softSkillsResult, 'soft');

            const assessment = {
                employee: {
                    name: `${userData.firstName} ${userData.lastName}`,
                    position: userData.userRole,
                    img: 'people.png',
                    email: userData.userEmail
                },
                digital: {
                    status: digitalResult ? 'completed' : 'pending',
                    score: digitalScore,
                    learningPath: digitalPath,
                    summary: digitalSummary
                },
                soft: {
                    status: softSkillsResult ? 'completed' : 'pending',
                    score: softSkillsScore,
                    learningPath: softSkillsPath,
                    summary: softSkillsSummary
                },
                createdAt: createdAt
            };

            assessments.push(assessment);
        });

        await Promise.all(userPromises);

        assessments.sort((a, b) => {
            if (a.createdAt && b.createdAt) {
                return b.createdAt.getTime() - a.createdAt.getTime();
            } else if (a.createdAt) {
                return -1;
            } else if (b.createdAt) {
                return 1;
            } else {
                return 0;
            }
        });

        cachedTableData = assessments.slice(0, 10);
        // Convert Date objects to ISO strings before caching
        const cacheData = cachedTableData.map(item => ({
            ...item,
            createdAt: item.createdAt ? item.createdAt.toISOString() : null
        }));
        sessionStorage.setItem(`${cachePrefix}tableData`, JSON.stringify(cacheData));
        sessionStorage.setItem(`${cachePrefix}tableLastFetchTime`, currentTime.toString());

        updateTableUI(cachedTableData);
        tableDataLoaded = true;
        checkAllDataLoaded();
        return cachedTableData;

    } catch (error) {
        console.error('Error initializing table:', error);
        tableDataLoaded = true;
        checkAllDataLoaded();
        return null;
    }
}

function updateTableUI(data) {
    const tableBody = document.querySelector('table tbody');
    if (!tableBody) {
        console.error("Table body element not found.");
        return;
    }

    tableBody.innerHTML = '';

    data.forEach(item => {
        const row = document.createElement('tr');
        row.classList.add(
            'border-t',
            'border-gray-200',
            'dark:border-gray-700',
            'hover:bg-gray-50',
            'dark:hover:bg-gray-800',
            'transition-colors',
            'table-row'
        );

        // Store data attributes
        row.setAttribute('data-email', item.employee.email);
        row.setAttribute('data-company', userCompany);
        // Store employee name and position for skills gap analysis
        row.setAttribute('data-employee-name', item.employee.name);
        row.setAttribute('data-employee-position', item.employee.position);

        // Generate assessment type display
        const hasDigital = item.digital.status === 'completed';
        const hasSoft = item.soft.status === 'completed';
        let assessmentTypeDisplay = '';

        if (hasDigital && hasSoft) {
            assessmentTypeDisplay = `<span class="bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400 px-2 py-1 text-xs rounded-full">Digital & Soft Skills</span>`;
        } else if (hasDigital) {
            assessmentTypeDisplay = `<span class="bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 px-2 py-1 text-xs rounded-full">Digital Skills</span>`;
        } else if (hasSoft) {
            assessmentTypeDisplay = `<span class="bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400 px-2 py-1 text-xs rounded-full">Soft Skills</span>`;
        } else {
            assessmentTypeDisplay = `<span class="bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400 px-2 py-1 text-xs rounded-full">Pending</span>`;
        }

        // Format date
        const createdAtFormatted = formatDate(item.createdAt);

        // Generate row HTML with properly spaced columns
        row.innerHTML = `
            <td class="px-4 py-2 flex items-center gap-3 w-1/4">
                <img src="${item.employee.img}" alt="${item.employee.name}" class="w-8 h-8 rounded-full">
                <div>
                    <p class="font-medium text-sm">${item.employee.name}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${item.employee.position}</p>
                </div>
            </td>
            <td class="px-4 py-2 w-1/5">${assessmentTypeDisplay}</td>
            <td class="px-4 py-2 text-center w-1/12">
                ${(hasDigital || hasSoft) ? `
                    <button class="inline-flex items-center justify-center p-1.5 rounded-lg text-blue-600 hover:text-blue-800 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                            title="View Skills Gap Analysis"
                            onclick="handleSkillsGapClick(event, this)"
                            data-email="${item.employee.email}"
                            data-company="${userCompany}"
                            data-has-digital="${hasDigital}"
                            data-has-soft="${hasSoft}"
                            data-learning-path="${hasDigital ? item.digital.learningPath : item.soft.learningPath}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bar-chart-3">
                            <path d="M3 3v18h18"/>
                            <path d="M18 17V9"/>
                            <path d="M13 17V5"/>
                            <path d="M8 17v-3"/>
                        </svg>
                    </button>
                ` : ''}
            </td>
            <td class="px-4 py-2 w-1/3">
                <div class="flex flex-col gap-1">
                    ${hasDigital ? `
                        <div class="flex items-center gap-1">
                            <span class="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span class="text-xs">Digital: ${item.digital.learningPath}</span>
                        </div>
                    ` : ''}
                    ${hasSoft ? `
                        <div class="flex items-center gap-1">
                            <span class="inline-block w-2 h-2 bg-purple-500 rounded-full"></span>
                            <span class="text-xs">Soft: ${item.soft.learningPath}</span>
                        </div>
                    ` : ''}
                </div>
            </td>
            <td class="px-4 py-2 text-xs w-1/6">${createdAtFormatted}</td>
        `;

        tableBody.appendChild(row);
    });

    // Hide loading states
    document.querySelectorAll('.skeleton-row').forEach(row => row.style.display = 'none');
    document.querySelector('.table-header').style.display = 'table-header-group';

    // Update recent assessments card display
    const recentAssessmentsCard = document.querySelector('.mt-8 .card');
    if (recentAssessmentsCard) {
        const skeletonCard = recentAssessmentsCard.querySelector('.skeleton-card');
        const cardContent = recentAssessmentsCard.querySelector('.card-content');

        if (skeletonCard) skeletonCard.style.display = 'none';
        if (cardContent) cardContent.style.display = 'block';
    }
}

async function handleSkillsGapClick(event, button) {
    event.preventDefault();
    event.stopPropagation();

    // Add loading class to the button
    button.classList.add('loading-border');

    // Log the current state for debugging
    console.log('Skills gap click - Current state:', {
        isDemoMode: window.isDemoMode,
        userCompany: window.userCompany,
        originalUserCompany: window.originalUserCompany
    });

    const email = button.getAttribute('data-email');

    // Determine which company to use
    let company;
    if (window.isDemoMode === true) {
        company = 'Barefoot eLearning';
        console.log('Using demo company: Barefoot eLearning');
    } else {
        company = button.getAttribute('data-company') || window.userCompany || window.originalUserCompany;
        console.log('Using user company:', company);
    }

    if (!email || !company) {
        console.error('Missing email or company data');
        showNotification('Skills gap analysis still pending for this user, check again later', 'info');
        // Remove loading class if there's an error
        button.classList.remove('loading-border');
        return;
    }

    try {
        showLoadingOverlay();

        // Fetch the user document to get assessment IDs
        const userRef = db.collection('companies')
                         .doc(company)
                         .collection('users')
                         .doc(email);

        const userDoc = await userRef.get();
        if (!userDoc.exists) {
            throw new Error('User not found');
        }

        const userData = userDoc.data();

        // Get digital skills assessment data (original)
        const digitalSkillsData = await fetchDigitalSkillsData(userRef, userData);

        // Get soft skills assessment data (new)
        const softSkillsData = await fetchSoftSkillsData(userRef, userData);

        // Get employee details from the row's data attributes
        const row = button.closest('tr');
        const employeeName = row.getAttribute('data-employee-name');
        const employeePosition = row.getAttribute('data-employee-position');

        // Store specific learning paths for each type
        let digitalPath = null;
        let softSkillsPath = null;

        if (digitalSkillsData && digitalSkillsData.report) {
            digitalPath = digitalSkillsData.report.learningPath;
            console.log('Digital Skills Learning Path:', digitalPath);
        }

        if (softSkillsData && softSkillsData.report) {
            softSkillsPath = softSkillsData.report.learningPath;
            console.log('Soft Skills Learning Path:', softSkillsPath);
        }

        // Create a combined data structure with both analysis types
        const combinedData = {
            metadata: {
                userCompany: company,
                userId: email,
                employeeName: employeeName,
                role: employeePosition,
                isEnrollable: !(userData.enrollmentStatus === 'enrolled' || userData.enrollmentStatus === 'processing'),
                availableAnalysisTypes: [],
                // Store both paths separately in metadata
                pathsByType: {
                    digitalSkills: digitalPath,
                    softSkills: softSkillsPath
                }
            }
        };

        // Decide which path to use as the default currentPath
        // Digital skills take precedence by default, but both are available in metadata
        combinedData.metadata.currentPath = digitalPath || softSkillsPath;
        console.log('Default currentPath for skills gap modal:', combinedData.metadata.currentPath);

        // Only add data structures that exist
        if (digitalSkillsData && digitalSkillsData.report && digitalSkillsData.report.competencyAnalysis) {
            // Include the specific learning path in each analysis type data
            digitalSkillsData.report.analysisType = 'digitalSkills';
            combinedData.digitalSkills = digitalSkillsData;
            combinedData.metadata.availableAnalysisTypes.push('digitalSkills');
        }

        if (softSkillsData && softSkillsData.report && softSkillsData.report.competencyAnalysis) {
            // Include the specific learning path in each analysis type data
            softSkillsData.report.analysisType = 'softSkills';
            combinedData.softSkills = softSkillsData;
            combinedData.metadata.availableAnalysisTypes.push('softSkills');
        }

        // Only proceed if at least one analysis type is available
        if (combinedData.metadata.availableAnalysisTypes.length === 0) {
            throw new Error('No skills analysis data available for this user');
        }

        console.log('Passing learning paths to skills gap modal:', combinedData.metadata.pathsByType);
        await window.showSkillsGapAnalysis(combinedData);

    } catch (error) {
        console.error('Error showing skills gap analysis:', error);
        showNotification('Skills gap analysis still pending for this user, check again later', 'info');
    } finally {
        hideLoadingOverlay();
        // Remove loading class when operation completes (success or error)
        button.classList.remove('loading-border');
    }
}

// Helper function to map section numbers to learning path names
function mapSectionToLearningPath(sectionNumber) {
    if (!sectionNumber && sectionNumber !== 0) return null;

    switch (Number(sectionNumber)) {
        case 1: return 'essentials';
        case 2: return 'intermediate';
        case 3: return 'advanced';
        case 4: return 'champions';
        default: return null;
    }
}

// Helper function to fetch digital skills data
async function fetchDigitalSkillsData(userRef, userData) {
    try {
        const lastAssessmentId = userData.lastAssessmentId;
        if (!lastAssessmentId) return null;

        // Fetch the latest assessment summary first
        const summarySnapshot = await userRef
            .collection('assessmentSummaries')
            .orderBy('timestamp', 'desc')
            .limit(1)
            .get();

        // Get currentSection from the summary if available
        let currentSection = null;
        let learningPath = null;

        if (!summarySnapshot.empty) {
            const summaryData = summarySnapshot.docs[0].data();
            currentSection = summaryData.currentSection;
            learningPath = mapSectionToLearningPath(currentSection);
            console.log('Digital Skills - Current Section:', currentSection, 'Mapped Learning Path:', learningPath);
        }

        // If we couldn't determine the learning path from summary, fall back to userData
        if (!learningPath) {
            learningPath = userData.currentPath;
            console.log('Digital Skills - Fallback to userData.currentPath:', learningPath);
        }

        const assessmentDoc = await userRef
            .collection('assessmentResults')
            .doc(lastAssessmentId)
            .get();

        if (!assessmentDoc.exists) return null;

        const assessmentData = assessmentDoc.data();

        return {
            report: {
                employeeName: userData.firstName + ' ' + userData.lastName,
                role: userData.userRole,
                learningPath: learningPath, // Use the learning path from summary
                currentPath: learningPath, // Added for consistency
                competencyAnalysis: assessmentData.competencyAnalysis,
                summary: assessmentData.analysisSummary,
                enrollmentStatus: userData.enrollmentStatus || 'not_enrolled',
                email: userData.userEmail
            },
            recommendations: assessmentData.courseRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification
            })) || [],
            other_learning_paths_courses: assessmentData.otherPathRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                learningPath: rec.learningPath
            })) || []
        };
    } catch (error) {
        console.error('Error fetching digital skills data:', error);
        return null;
    }
}

// Helper function to fetch soft skills data
async function fetchSoftSkillsData(userRef, userData) {
    try {
        const lastSoftSkillsAssessmentId = userData.lastSoftSkillsAssessmentId;
        if (!lastSoftSkillsAssessmentId) return null;

        // Fetch the latest soft skills assessment summary first
        const summarySnapshot = await userRef
            .collection('softSkillsSummaries')
            .orderBy('timestamp', 'desc')
            .limit(1)
            .get();

        // Get currentSection from the summary if available
        let currentSection = null;
        let learningPath = null;

        if (!summarySnapshot.empty) {
            const summaryData = summarySnapshot.docs[0].data();
            currentSection = summaryData.currentSection;
            learningPath = mapSectionToLearningPath(currentSection);
            console.log('Soft Skills - Current Section:', currentSection, 'Mapped Learning Path:', learningPath);
        }

        // Always fetch assessment data regardless of learning path source
        const assessmentDoc = await userRef
            .collection('softSkillsAssessmentResults')
            .doc(lastSoftSkillsAssessmentId)
            .get();

        if (!assessmentDoc.exists) return null;

        const assessmentData = assessmentDoc.data();

        // If no learning path from summary, try to get from assessment or user data
        if (!learningPath) {
            if (assessmentData.metadata?.learningPath) {
                learningPath = assessmentData.metadata.learningPath;
                console.log('Soft Skills - Fallback to assessment metadata:', learningPath);
            } else if (userData.currentPath) {
                learningPath = userData.currentPath;
                console.log('Soft Skills - Fallback to userData.currentPath:', learningPath);
            }
        }

        // Return the soft skills data with appropriate learning path
        return {
            report: {
                employeeName: userData.firstName + ' ' + userData.lastName,
                role: userData.userRole,
                learningPath: learningPath, // Use the learning path from summary or fallback
                currentPath: learningPath, // Added for consistency
                competencyAnalysis: assessmentData.competencyAnalysis,
                summary: assessmentData.analysisSummary,
                enrollmentStatus: userData.enrollmentStatus || 'not_enrolled',
                email: userData.userEmail
            },
            recommendations: assessmentData.courseRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification
            })) || [],
            other_learning_paths_courses: assessmentData.otherPathRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                learningPath: rec.learningPath
            })) || []
        };
    } catch (error) {
        console.error('Error fetching soft skills data:', error);
        return null;
    }
}


function checkAllDataLoaded() {
    if (dashboardDataLoaded && tableDataLoaded) {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        // Reset flags for next load
        dashboardDataLoaded = false;
        tableDataLoaded = false;
    }
}

function initializeDashboard(userCompany) {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }

    // Track dashboard page access
    trackMilestone('dashboard_page_viewed', {
        userCompany: userCompany
    });

    // Set initial state - hide content, show skeletons
    document.querySelectorAll('.card-content').forEach(content => content.style.display = 'none');
    document.querySelectorAll('.card-empty').forEach(empty => empty.style.display = 'none');
    document.querySelectorAll('.skeleton-card').forEach(card => card.style.display = 'block');

    updateDashboardData(userCompany);
    initializeTable(userCompany);
}

function generateSummaryText(summary) {
    if (!summary || !summary.sectionScores) {
        return "No assessment data available.";
    }

    const sections = ['Essentials', 'Intermediate', 'Advanced', 'Champions'];
    const passThreshold = 70;

    let lastPassedSection = null;
    let recommendedPath = null;
    const sectionTexts = [];

    for (const section of sections) {
        if (summary.sectionScores[section] > 0) {
            const score = summary.sectionScores[section];
            const totalQuestions = summary.questionsPerSection[section];
            const percentage = ((score / totalQuestions) * 100).toFixed(0);
            sectionTexts.push(`${section} ${percentage}% (${score}/${totalQuestions})`);

            if (parseInt(percentage) >= passThreshold) {
                lastPassedSection = section;
            } else {
                recommendedPath = section;
                break;
            }
        } else {
            if (!recommendedPath) {
                recommendedPath = section;
            }
            break;
        }
    }

    if (sectionTexts.length === 0) {
        return "Assessment not started. Begin with Essentials.";
    }

    let summaryText = `Completed: ${sectionTexts.join(', ')}. `;

    if (recommendedPath) {
        summaryText += `Focus: Upskill in ${recommendedPath}.`;
        if (recommendedPath === 'Essentials') {
            summaryText += " Other Sections Not Yet Accessible.";
        }
    } else {
        summaryText += "All levels passed. Consider advanced specialization.";
    }

    return summaryText;
}

function handleAllAssessmentsClick() {
    const assessmentsLinks = document.querySelectorAll('a[href="#assessments"], nav a');
    let assessmentsLink = null;
    for (const link of assessmentsLinks) {
        if (link.textContent.trim().toLowerCase() === 'assessments') {
            assessmentsLink = link;
            break;
        }
    }

    if (assessmentsLink) {
        assessmentsLink.click();
    } else {
        console.error('Assessments link not found');
        const mainContent = document.querySelector('#main-content');
        loadAssessmentsPage(mainContent);
        updateActiveNavLink('assessments');
    }
}

function setupEventListeners() {
    const allAssessmentsBtn = document.getElementById('all-assessments-btn');
    if (allAssessmentsBtn) {
        allAssessmentsBtn.addEventListener('click', handleAllAssessmentsClick);
    } else {
        console.error('All assessments button not found');
    }
}

function initializeDashboardCards() {
    const learningPathCards = document.querySelectorAll('.card[data-path]');
    learningPathCards.forEach(card => {
        const category = card.getAttribute('data-path');
        const enrollButton = card.querySelector('.enroll-button');

        card.addEventListener('click', () => {
            handleEnrollButtonClick(category);
        });

        if (enrollButton) {
            enrollButton.addEventListener('click', (event) => {
                event.stopPropagation();
                handleEnrollButtonClick(category);
            });
        }
    });
}

function handleEnrollButtonClick(category) {
    const mainContent = document.querySelector('#main-content');
    loadDetailContent(mainContent, category);
}

function setupDashboardEventListeners() {
    initializeDashboardCards();

    document.addEventListener('dashboardEnrollClicked', (event) => {
        const category = event.detail.category;
        handleEnrollButtonClick(category);
    });
}

function handleScrollEvent() {
    const allAssessmentsButton = document.getElementById('all-assessments-btn');
    const tableContainer = document.querySelector('.relative.overflow-x-auto');

    if (tableContainer && allAssessmentsButton) {
        tableContainer.addEventListener('scroll', function() {
            if (tableContainer.scrollTop + tableContainer.clientHeight >= tableContainer.scrollHeight) {
                allAssessmentsButton.classList.add('visible');
            } else {
                allAssessmentsButton.classList.remove('visible');
            }
        });
    }
}


const style = document.createElement('style');
style.textContent = `
    .table-row-hover {
        transition: background-color 0.2s ease;
    }

    .table-row-hover:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .dark .table-row-hover:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    @keyframes pulseBorder {
        0% {
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
        }
        50% {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
        }
    }

    .loading-border {
        animation: pulseBorder 1.5s infinite;
        border-radius: 6px;
    }

    .dark .loading-border {
        animation: pulseBorder 1.5s infinite;
        border-radius: 6px;
    }
`;
document.head.appendChild(style);



