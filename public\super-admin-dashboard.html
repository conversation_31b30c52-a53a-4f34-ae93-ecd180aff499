<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Dashboard - Skills Assess</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="super-admin-dashboard.css">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Date picker for filtering -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Lottie for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <img src="logo.png" alt="Skills Assess" class="logo">
                <div class="header-title">
                    <h1>Super Admin Dashboard</h1>
                    <p>Platform Analytics & Usage Tracking</p>
                </div>
            </div>
            <div class="header-right">
                <div class="admin-info">
                    <span class="admin-badge">Super Admin</span>
                    <span class="admin-email" id="admin-email"><EMAIL></span>
                </div>
                <button id="logout-btn" class="logout-btn">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                    Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navigation">
        <div class="nav-content">
            <button class="nav-item active" data-section="overview">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Overview
            </button>
            <button class="nav-item" data-section="admins">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                Admin Accounts
            </button>
            <button class="nav-item" data-section="companies">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                Companies
            </button>
            <button class="nav-item" data-section="users">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                Users
            </button>
            <button class="nav-item" data-section="assessments">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Assessments
            </button>
            <button class="nav-item" data-section="analytics">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Analytics
            </button>
            <button class="nav-item" data-section="security">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Security Audit
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-container">
            <!-- Overview Section -->
            <section id="overview-section" class="dashboard-section active">
                <div class="overview-header">
                    <div class="overview-controls">
                        <input type="text" id="date-range-picker" placeholder="Select date range" readonly>
                        <button id="refresh-data" class="refresh-btn">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Modern Metric Cards -->
                <div class="overview-metrics" id="metrics-container">
                    <!-- Skeleton loaders will be replaced with actual content -->
                    <div class="overview-metric-card skeleton-card">
                        <div class="skeleton-content">
                            <div class="skeleton-text small"></div>
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-text tiny"></div>
                        </div>
                    </div>

                    <div class="overview-metric-card skeleton-card">
                        <div class="skeleton-content">
                            <div class="skeleton-text small"></div>
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-text tiny"></div>
                        </div>
                    </div>

                    <div class="overview-metric-card skeleton-card">
                        <div class="skeleton-content">
                            <div class="skeleton-text small"></div>
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-text tiny"></div>
                        </div>
                    </div>

                    <div class="overview-metric-card skeleton-card">
                        <div class="skeleton-content">
                            <div class="skeleton-text small"></div>
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-text tiny"></div>
                        </div>
                    </div>
                </div>

                <!-- Modern Charts Section -->
                <div class="overview-charts">
                    <div class="overview-chart-card">
                        <div class="chart-header">
                            <h3>Platform Growth</h3>
                            <select id="growth-timeframe" class="chart-select">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                            </select>
                        </div>
                        <div class="chart-container" aria-label="Platform growth chart">
                            <!-- Skeleton loader for chart -->
                            <div class="chart-skeleton" id="growth-chart-skeleton">
                                <div class="skeleton-chart-header">
                                    <div class="skeleton-text medium"></div>
                                    <div class="skeleton-text small"></div>
                                </div>
                                <div class="skeleton-chart-body">
                                    <div class="skeleton-chart-area"></div>
                                    <div class="skeleton-chart-legend">
                                        <div class="skeleton-text tiny"></div>
                                        <div class="skeleton-text tiny"></div>
                                    </div>
                                </div>
                            </div>
                            <canvas id="growth-chart" style="display: none;"></canvas>
                        </div>
                    </div>

                    <div class="overview-chart-card">
                        <div class="chart-header">
                            <h3>Assessment Completion Rate</h3>
                        </div>
                        <div class="chart-container" aria-label="Assessment completion rate chart">
                            <!-- Skeleton loader for chart -->
                            <div class="chart-skeleton" id="completion-chart-skeleton">
                                <div class="skeleton-chart-body">
                                    <div class="skeleton-chart-area"></div>
                                    <div class="skeleton-chart-legend">
                                        <div class="skeleton-text tiny"></div>
                                        <div class="skeleton-text tiny"></div>
                                        <div class="skeleton-text tiny"></div>
                                    </div>
                                </div>
                            </div>
                            <canvas id="completion-chart" style="display: none;"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Other sections with skeleton loaders -->
            <section id="admins-section" class="dashboard-section">
                <div class="section-content">
                    <div class="section-skeleton">
                        <div class="skeleton-header">
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-controls">
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                            </div>
                        </div>
                        <div class="skeleton-table">
                            <div class="skeleton-row" style="--delay: 0s;"></div>
                            <div class="skeleton-row" style="--delay: 0.1s;"></div>
                            <div class="skeleton-row" style="--delay: 0.2s;"></div>
                            <div class="skeleton-row" style="--delay: 0.3s;"></div>
                            <div class="skeleton-row" style="--delay: 0.4s;"></div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="companies-section" class="dashboard-section">
                <div class="section-content">
                    <div class="section-skeleton">
                        <div class="skeleton-header">
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-controls">
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                            </div>
                        </div>
                        <div class="skeleton-table">
                            <div class="skeleton-row" style="--delay: 0s;"></div>
                            <div class="skeleton-row" style="--delay: 0.1s;"></div>
                            <div class="skeleton-row" style="--delay: 0.2s;"></div>
                            <div class="skeleton-row" style="--delay: 0.3s;"></div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="users-section" class="dashboard-section">
                <div class="section-content">
                    <div class="section-skeleton">
                        <div class="skeleton-header">
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-controls">
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                            </div>
                        </div>
                        <div class="skeleton-table">
                            <div class="skeleton-row" style="--delay: 0s;"></div>
                            <div class="skeleton-row" style="--delay: 0.1s;"></div>
                            <div class="skeleton-row" style="--delay: 0.2s;"></div>
                            <div class="skeleton-row" style="--delay: 0.3s;"></div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="assessments-section" class="dashboard-section">
                <div class="section-content">
                    <div class="section-skeleton">
                        <div class="skeleton-header">
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-controls">
                                <div class="skeleton-text small"></div>
                                <div class="skeleton-text small"></div>
                            </div>
                        </div>
                        <div class="skeleton-charts">
                            <div class="skeleton-chart"></div>
                            <div class="skeleton-chart"></div>
                        </div>
                        <div class="skeleton-table">
                            <div class="skeleton-row" style="--delay: 0s;"></div>
                            <div class="skeleton-row" style="--delay: 0.1s;"></div>
                            <div class="skeleton-row" style="--delay: 0.2s;"></div>
                            <div class="skeleton-row" style="--delay: 0.3s;"></div>
                            <div class="skeleton-row" style="--delay: 0.4s;"></div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="analytics-section" class="dashboard-section">
                <div class="section-content">
                    <div class="section-skeleton">
                        <div class="skeleton-analytics-grid">
                            <div class="skeleton-analytics-card"></div>
                            <div class="skeleton-analytics-card"></div>
                            <div class="skeleton-analytics-card"></div>
                            <div class="skeleton-analytics-card"></div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="security-section" class="dashboard-section">
                <div class="section-content">
                    <div class="section-skeleton">
                        <div class="skeleton-security-stats">
                            <div class="skeleton-stat-card"></div>
                            <div class="skeleton-stat-card"></div>
                            <div class="skeleton-stat-card"></div>
                            <div class="skeleton-stat-card"></div>
                        </div>
                        <div class="skeleton-table">
                            <div class="skeleton-row" style="--delay: 0s;"></div>
                            <div class="skeleton-row" style="--delay: 0.1s;"></div>
                            <div class="skeleton-row" style="--delay: 0.2s;"></div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- User Journey Modal -->
    <div id="user-journey-modal" class="journey-modal-overlay">
        <div class="journey-modal">
            <div class="journey-modal-header">
                <div class="journey-modal-title">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <div>
                        <h3 id="journey-admin-name">User Journey Analytics</h3>
                        <p id="journey-admin-email" class="text-sm text-gray-500"></p>
                    </div>
                </div>
                <button id="close-journey-modal" class="journey-modal-close">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="journey-modal-content">
                <!-- Loading State -->
                <div id="journey-loading" class="journey-loading">
                    <div class="journey-skeleton">
                        <div class="journey-skeleton-header">
                            <div class="skeleton-text large"></div>
                            <div class="skeleton-text medium"></div>
                        </div>
                        <div class="journey-skeleton-stats">
                            <div class="skeleton-stat-card"></div>
                            <div class="skeleton-stat-card"></div>
                            <div class="skeleton-stat-card"></div>
                        </div>
                        <div class="journey-skeleton-timeline">
                            <div class="skeleton-timeline-item"></div>
                            <div class="skeleton-timeline-item"></div>
                            <div class="skeleton-timeline-item"></div>
                            <div class="skeleton-timeline-item"></div>
                        </div>
                    </div>
                </div>

                <!-- No Data State -->
                <div id="journey-no-data" class="journey-no-data" style="display: none;">
                    <div class="no-data-illustration">
                        <svg class="w-24 h-24 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h4>No Journey Data Available</h4>
                    <p>Journey data will be displayed once the user starts interacting with the dashboard. This includes:</p>
                    <ul class="no-data-features">
                        <li>✓ Page navigation and feature usage</li>
                        <li>✓ Assessment and invitation activities</li>
                        <li>✓ Session tracking and milestones</li>
                        <li>✓ Learning path interactions</li>
                    </ul>
                </div>

                <!-- Journey Data Content -->
                <div id="journey-content" style="display: none;">
                    <!-- Journey Overview Stats -->
                    <div class="journey-stats">
                        <div class="journey-stat-card">
                            <div class="stat-icon bg-blue-100 text-blue-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value" id="total-sessions">-</div>
                                <div class="stat-label">Total Sessions</div>
                            </div>
                        </div>
                        <div class="journey-stat-card">
                            <div class="stat-icon bg-green-100 text-green-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value" id="features-used">-</div>
                                <div class="stat-label">Features Used</div>
                            </div>
                        </div>
                        <div class="journey-stat-card">
                            <div class="stat-icon bg-purple-100 text-purple-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value" id="milestones-achieved">-</div>
                                <div class="stat-label">Milestones</div>
                            </div>
                        </div>
                    </div>

                    <!-- Journey Timeline -->
                    <div class="journey-timeline-section">
                        <h4 class="journey-section-title">Recent Activity Timeline</h4>
                        <div id="journey-timeline" class="journey-timeline">
                            <!-- Timeline items will be populated here -->
                        </div>
                    </div>

                    <!-- Features Usage -->
                    <div class="journey-features-section">
                        <h4 class="journey-section-title">Feature Usage</h4>
                        <div id="journey-features" class="journey-features-grid">
                            <!-- Feature usage will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="warning-modal.js"></script>
    <script src="super-admin-login.js"></script>
    <script src="super-admin-dashboard.js"></script>
</body>
</html>
