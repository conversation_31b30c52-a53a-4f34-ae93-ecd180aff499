<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced User Journey Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button.access {
            background: #6b7280;
        }
        .test-button.usage {
            background: #10b981;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f3f4f6;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .tracking-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Enhanced User Journey Tracking Test</h1>
    <p>This page tests the new granular tracking system that differentiates between "accessed" and "used" features.</p>

    <div class="test-section">
        <h2>Feature Access vs Usage Testing</h2>
        <p>Test the difference between accessing a feature (navigation) and using it (meaningful interaction).</p>
        
        <h3>Dashboard Feature</h3>
        <button class="test-button access" onclick="testFeatureAccess('dashboard')">
            Simulate Dashboard Access (Navigation)
        </button>
        <button class="test-button usage" onclick="testFeatureUsage('dashboard', 'assessments_navigation')">
            Simulate Dashboard Usage (Assessments Button Click)
        </button>
        <button class="test-button usage" onclick="testFeatureUsage('dashboard', 'learning_path_enrollment')">
            Simulate Dashboard Usage (Enrollment Click)
        </button>

        <h3>Invitations Feature</h3>
        <button class="test-button access" onclick="testFeatureAccess('invitations')">
            Simulate Invitations Access (Navigation)
        </button>
        <button class="test-button usage" onclick="testInvitationUsage('invitation_sent')">
            Simulate Invitations Usage (Send Invitation)
        </button>
        <button class="test-button usage" onclick="testInvitationUsage('assessment_completed')">
            Simulate Invitations Usage (Complete Assessment)
        </button>

        <h3>Skills Gap Analysis</h3>
        <button class="test-button usage" onclick="testSkillsGapUsage('dashboard')">
            Simulate Skills Gap Usage (From Dashboard)
        </button>
        <button class="test-button usage" onclick="testSkillsGapUsage('assessment')">
            Simulate Skills Gap Usage (From Assessment)
        </button>

        <h3>Assessments Feature</h3>
        <button class="test-button access" onclick="testFeatureAccess('assessments')">
            Simulate Assessments Access (Navigation)
        </button>
        <button class="test-button usage" onclick="testAssessmentUsage('assessment_completed')">
            Simulate Assessment Usage (Complete Assessment)
        </button>
    </div>

    <div class="test-section">
        <h2>Email Verification Testing</h2>
        <button class="test-button" onclick="testEmailVerification('verified')">
            Simulate Email Verification
        </button>
        <button class="test-button" onclick="testEmailVerification('failed')">
            Simulate Email Verification Failed
        </button>
    </div>

    <div class="test-section">
        <h2>Current Session Info</h2>
        <button class="test-button" onclick="showSessionInfo()">
            Show Current Session
        </button>
        <div id="session-info" class="status"></div>
    </div>

    <div class="test-section">
        <h2>Tracking Log</h2>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
        <div id="tracking-log" class="tracking-log"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>

    <!-- User Journey Tracker -->
    <script src="user-journey-tracker.js"></script>

    <script>
        // Initialize Firebase (same config as other pages)
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        // Initialize tracking when user is authenticated
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                log(`User authenticated: ${user.email}`);
                if (window.UserJourneyTracker) {
                    window.UserJourneyTracker.initialize();
                    log('User Journey Tracker initialized');
                }
            } else {
                log('User not authenticated - please log in first');
            }
        });

        // Logging function
        function log(message) {
            const logElement = document.getElementById('tracking-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[Enhanced Tracking Test] ${message}`);
        }

        function clearLog() {
            document.getElementById('tracking-log').innerHTML = '';
        }

        // Test functions
        function testFeatureAccess(featureName) {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.updateFeatureAccess(featureName);
                log(`✓ Feature ACCESS tracked: ${featureName}`);
            } else {
                log('✗ UserJourneyTracker not available');
            }
        }

        function testFeatureUsage(featureName, action) {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.updateFeatureUsage(featureName, { action: action, testMode: true });
                log(`✓ Feature USAGE tracked: ${featureName} - ${action}`);
            } else {
                log('✗ UserJourneyTracker not available');
            }
        }

        function testInvitationUsage(action) {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackInviteUsage(action, { testMode: true, timestamp: new Date() });
                log(`✓ Invitation USAGE tracked: ${action}`);
            } else {
                log('✗ UserJourneyTracker not available');
            }
        }

        function testSkillsGapUsage(source) {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackSkillsGapUsage(source, { testMode: true, timestamp: new Date() });
                log(`✓ Skills Gap Analysis USAGE tracked from: ${source}`);
            } else {
                log('✗ UserJourneyTracker not available');
            }
        }

        function testAssessmentUsage(action) {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackAssessmentUsage(action, { testMode: true, timestamp: new Date() });
                log(`✓ Assessment USAGE tracked: ${action}`);
            } else {
                log('✗ UserJourneyTracker not available');
            }
        }

        function testEmailVerification(status) {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackEmailVerification(status);
                log(`✓ Email verification tracked: ${status}`);
            } else {
                log('✗ UserJourneyTracker not available');
            }
        }

        function showSessionInfo() {
            if (window.UserJourneyTracker) {
                const session = window.UserJourneyTracker.getCurrentSession();
                const info = `
                    Session ID: ${session.sessionId}<br>
                    Current Page: ${session.currentPage}<br>
                    Session Start: ${session.sessionStartTime}
                `;
                document.getElementById('session-info').innerHTML = info;
                document.getElementById('session-info').className = 'status success';
                log('Session info displayed');
            } else {
                document.getElementById('session-info').innerHTML = 'UserJourneyTracker not available';
                document.getElementById('session-info').className = 'status error';
            }
        }

        // Initial log
        log('Enhanced User Journey Tracking Test Page Loaded');
    </script>
</body>
</html>
