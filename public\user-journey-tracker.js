// User Journey Tracking System for Super Admin Portal
// This module tracks all user interactions and stores them in the admin document

(function() {
    'use strict';

    // Initialize tracking system
    let trackingEnabled = true;
    let sessionId = null;
    let currentPage = null;
    let sessionStartTime = null;

    // Generate unique session ID
    function generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    // Initialize tracking session
    function initializeTracking() {
        sessionId = generateSessionId();
        sessionStartTime = new Date();
        currentPage = window.location.pathname.split('/').pop() || 'unknown';
        
        console.log('User journey tracking initialized:', { sessionId, currentPage });
        
        // Track session start
        trackEvent('session_started', {
            page: currentPage,
            userAgent: navigator.userAgent,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            referrer: document.referrer || 'direct'
        });

        // Set up navigation link monitoring
        setupNavigationTracking();
    }

    // Monitor navigation links for active state changes
    function setupNavigationTracking() {
        // Check for active nav links periodically
        setInterval(() => {
            const activeNavLink = document.querySelector('nav a.active, #navigation-drawer nav a.active, header nav a.active');
            if (activeNavLink) {
                const linkText = activeNavLink.textContent.trim().toLowerCase();
                const dataContent = activeNavLink.getAttribute('data-content');
                const trackingData = activeNavLink.getAttribute('data-track');
                
                // Track page access based on active navigation
                trackPageAccess(linkText, dataContent, trackingData);
            }
        }, 1000); // Check every second

        // Also set up mutation observer to catch immediate changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.matches('nav a, #navigation-drawer nav a, header nav a') && target.classList.contains('active')) {
                        const linkText = target.textContent.trim().toLowerCase();
                        const dataContent = target.getAttribute('data-content');
                        const trackingData = target.getAttribute('data-track');
                        
                        // Track page access
                        trackPageAccess(linkText, dataContent, trackingData);
                    }
                }
            });
        });

        // Start observing
        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['class']
        });
    }

    // Track page access based on navigation
    function trackPageAccess(linkText, dataContent, trackingData) {
        const pageMap = {
            'dashboard': 'dashboard',
            'assessments': 'assessments',
            'learning paths': 'learningPaths',
            'metrics': 'reports',
            'reports': 'reports',
            'invitations': 'invitations',
            'invite': 'invitations'
        };

        const featureName = pageMap[linkText] || dataContent || trackingData || linkText;
        
        // Track milestone for page access
        trackMilestone(`${featureName}_accessed`, {
            page: featureName,
            navigationMethod: 'nav_link',
            linkText: linkText,
            timestamp: new Date()
        });

        // Update feature access in userJourney
        updateFeatureAccess(featureName);
    }

    // Update feature access tracking (navigation only)
    async function updateFeatureAccess(featureName) {
        if (!firebase.auth().currentUser) return;

        const user = firebase.auth().currentUser;
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);

                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    let userJourney = adminData.userJourney || {};

                    // Ensure features object exists
                    if (!userJourney.features) {
                        userJourney.features = {};
                    }

                    // Initialize feature tracking with both accessed and used states
                    if (!userJourney.features[featureName]) {
                        userJourney.features[featureName] = {
                            accessed: false,
                            used: false,
                            firstAccess: null,
                            lastAccess: null,
                            firstUse: null,
                            lastUse: null,
                            accessCount: 0,
                            useCount: 0
                        };
                    }

                    // Update access tracking
                    const feature = userJourney.features[featureName];
                    if (!feature.accessed) {
                        feature.firstAccess = timestamp;
                    }
                    feature.accessed = true;
                    feature.lastAccess = timestamp;
                    feature.accessCount = (feature.accessCount || 0) + 1;

                    // Update the document
                    transaction.update(adminRef, {
                        userJourney: userJourney,
                        lastModified: timestamp
                    });

                    console.log('Feature access tracked:', featureName);
                }
            });
        } catch (error) {
            console.error('Error updating feature access:', error);
        }
    }

    // New function to track actual feature usage (meaningful interactions)
    async function updateFeatureUsage(featureName, usageData = {}) {
        if (!firebase.auth().currentUser) return;

        const user = firebase.auth().currentUser;
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);

                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    let userJourney = adminData.userJourney || {};

                    // Ensure features object exists
                    if (!userJourney.features) {
                        userJourney.features = {};
                    }

                    // Initialize feature tracking if not exists
                    if (!userJourney.features[featureName]) {
                        userJourney.features[featureName] = {
                            accessed: false,
                            used: false,
                            firstAccess: null,
                            lastAccess: null,
                            firstUse: null,
                            lastUse: null,
                            accessCount: 0,
                            useCount: 0
                        };
                    }

                    // Update usage tracking
                    const feature = userJourney.features[featureName];
                    if (!feature.used) {
                        feature.firstUse = timestamp;
                    }
                    feature.used = true;
                    feature.lastUse = timestamp;
                    feature.useCount = (feature.useCount || 0) + 1;

                    // Store usage context
                    if (Object.keys(usageData).length > 0) {
                        if (!feature.usageHistory) {
                            feature.usageHistory = [];
                        }
                        feature.usageHistory.unshift({
                            timestamp: new Date(),
                            ...usageData
                        });
                        // Keep only last 10 usage records
                        if (feature.usageHistory.length > 10) {
                            feature.usageHistory = feature.usageHistory.slice(0, 10);
                        }
                    }

                    // Update the document
                    transaction.update(adminRef, {
                        userJourney: userJourney,
                        lastModified: timestamp
                    });

                    console.log('Feature usage tracked:', featureName, usageData);
                }
            });
        } catch (error) {
            console.error('Error updating feature usage:', error);
        }
    }

    // Track milestone achievements
    async function trackMilestone(milestoneName, milestoneData = {}) {
        if (!firebase.auth().currentUser) return;

        const user = firebase.auth().currentUser;
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);
                
                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    let userJourney = adminData.userJourney || {};

                    // Ensure milestones object exists
                    if (!userJourney.milestones) {
                        userJourney.milestones = {};
                    }

                    // Record milestone
                    userJourney.milestones[milestoneName] = {
                        achieved: true,
                        timestamp: timestamp,
                        ...milestoneData
                    };

                    // Also track as an event
                    const eventRecord = {
                        eventType: 'milestone_achieved',
                        milestoneName,
                        timestamp: new Date(),
                        sessionId,
                        ...milestoneData
                    };

                    if (!userJourney.events) userJourney.events = [];
                    userJourney.events.unshift(eventRecord);

                    // Keep only last 100 events
                    if (userJourney.events.length > 100) {
                        userJourney.events = userJourney.events.slice(0, 100);
                    }

                    // Update the document
                    transaction.update(adminRef, {
                        userJourney: userJourney,
                        lastModified: timestamp
                    });

                    console.log('Milestone tracked:', milestoneName, milestoneData);
                }
            });
        } catch (error) {
            console.error('Error tracking milestone:', error);
        }
    }

    // Main tracking function
    async function trackEvent(eventType, eventData = {}) {
        if (!trackingEnabled || !firebase.auth().currentUser) {
            return;
        }

        const user = firebase.auth().currentUser;
        const timestamp = new Date();

        const eventRecord = {
            eventType,
            timestamp: timestamp,
            sessionId,
            page: currentPage,
            userAgent: navigator.userAgent,
            data: eventData
        };

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);
                
                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    
                    // Initialize journey tracking if not exists
                    if (!adminData.userJourney) {
                        adminData.userJourney = {
                            firstLoginDate: adminData.createdAt || timestamp,
                            totalSessions: 0,
                            totalEvents: 0,
                            featuresUsed: [],
                            pages: [],
                            events: []
                        };
                    }

                    const journey = adminData.userJourney;
                    
                    // Update journey data
                    journey.totalEvents = (journey.totalEvents || 0) + 1;
                    journey.lastActivityDate = timestamp;
                    
                    // Track unique features used
                    if (eventType === 'feature_used' && eventData.feature) {
                        if (!journey.featuresUsed.includes(eventData.feature)) {
                            journey.featuresUsed.push(eventData.feature);
                        }
                    }
                    
                    // Track unique pages visited
                    if (eventType === 'page_view' && eventData.page) {
                        const pageRecord = journey.pages.find(p => p.page === eventData.page);
                        if (pageRecord) {
                            pageRecord.visitCount += 1;
                            pageRecord.lastVisit = timestamp;
                        } else {
                            journey.pages.push({
                                page: eventData.page,
                                firstVisit: timestamp,
                                lastVisit: timestamp,
                                visitCount: 1
                            });
                        }
                    }
                    
                    // Add event to events array (keep last 500 events)
                    if (!journey.events) {
                        journey.events = [];
                    }
                    journey.events.unshift(eventRecord);
                    
                    if (journey.events.length > 500) {
                        journey.events = journey.events.slice(0, 500);
                    }
                    
                    // Update session tracking for new sessions
                    if (eventType === 'session_started') {
                        journey.totalSessions = (journey.totalSessions || 0) + 1;
                        journey.lastSessionStart = timestamp;
                    }
                    
                    // Update the admin document
                    transaction.update(adminRef, {
                        userJourney: journey,
                        lastModified: firebase.firestore.FieldValue.serverTimestamp()
                    });
                }
            });
        } catch (error) {
            console.error('Error tracking user event:', error);
        }
    }

    // Track page views
    function trackPageView(pageName = null) {
        const page = pageName || currentPage;
        trackEvent('page_view', { page });
    }

    // Track feature usage (meaningful interactions only)
    function trackFeatureUsage(featureName, additionalData = {}) {
        // Update the new granular feature usage tracking
        updateFeatureUsage(featureName, additionalData);

        // Also track as an event for backwards compatibility
        trackEvent('feature_used', {
            feature: featureName,
            ...additionalData
        });
    }

    // Specific feature usage tracking functions
    function trackInvitationFeatureUsage(action, data = {}) {
        updateFeatureUsage('invitations', {
            action: action,
            ...data
        });

        // Track milestone for first meaningful use
        if (action === 'invitation_sent' || action === 'assessment_completed') {
            trackMilestone('invitations_feature_used', {
                action: action,
                ...data
            });
        }
    }

    function trackSkillsGapAnalysisUsage(source, data = {}) {
        updateFeatureUsage('skillsGapAnalysis', {
            source: source, // 'dashboard' or 'assessment'
            ...data
        });

        trackMilestone('skills_gap_analysis_used', {
            source: source,
            ...data
        });
    }

    function trackAssessmentFeatureUsage(action, data = {}) {
        updateFeatureUsage('assessments', {
            action: action,
            ...data
        });

        // Track milestone for assessment completion
        if (action === 'assessment_completed') {
            trackMilestone('assessment_completed', {
                ...data
            });
        }
    }

    function trackDashboardFeatureUsage(action, data = {}) {
        updateFeatureUsage('dashboard', {
            action: action,
            ...data
        });
    }

    function trackReportsFeatureUsage(action, data = {}) {
        updateFeatureUsage('reports', {
            action: action,
            ...data
        });
    }

    function trackLearningPathsFeatureUsage(action, data = {}) {
        updateFeatureUsage('learningPaths', {
            action: action,
            ...data
        });
    }

    // Track button clicks
    function trackButtonClick(buttonName, context = {}) {
        trackEvent('button_click', {
            button: buttonName,
            ...context
        });
    }

    // Track navigation
    function trackNavigation(from, to, method = 'click') {
        trackEvent('navigation', {
            from,
            to,
            method
        });
        currentPage = to;
    }

    // Track form submissions
    function trackFormSubmission(formName, success = true, additionalData = {}) {
        trackEvent('form_submission', {
            form: formName,
            success,
            ...additionalData
        });
    }

    // Track modal interactions
    function trackModalInteraction(modalName, action, additionalData = {}) {
        trackEvent('modal_interaction', {
            modal: modalName,
            action,
            ...additionalData
        });
    }

    // Track subscription events
    function trackSubscriptionEvent(eventType, subscriptionData = {}) {
        trackEvent('subscription_event', {
            subscriptionEventType: eventType,
            ...subscriptionData
        });
    }

    // Track search and filter usage
    function trackSearchFilter(type, query, results = null) {
        trackEvent('search_filter', {
            type,
            query,
            resultCount: results
        });
    }

    // Track time spent on page
    let pageStartTime = new Date();
    function trackTimeOnPage() {
        const timeSpent = new Date() - pageStartTime;
        trackEvent('time_on_page', {
            page: currentPage,
            timeSpent: Math.round(timeSpent / 1000) // seconds
        });
    }

    // Track session end
    function trackSessionEnd() {
        const sessionDuration = new Date() - sessionStartTime;
        trackEvent('session_ended', {
            duration: Math.round(sessionDuration / 1000), // seconds
            page: currentPage
        });
    }

    // Setup automatic tracking
    function setupAutomaticTracking() {
        // Track page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                trackTimeOnPage();
            }
        });

        // Track before unload
        window.addEventListener('beforeunload', () => {
            trackSessionEnd();
        });

        // Track clicks on tracked elements
        document.addEventListener('click', (event) => {
            const element = event.target;

            // Track navigation links (access only, not usage)
            if (element.matches('a[data-content]') || element.closest('a[data-content]')) {
                const link = element.matches('a[data-content]') ? element : element.closest('a[data-content]');
                const feature = link.getAttribute('data-content');
                // Only track navigation, not feature usage
                trackNavigation(currentPage, feature, 'navigation_click');
            }
            
            // Track buttons with data-track attribute
            if (element.matches('[data-track]') || element.closest('[data-track]')) {
                const button = element.matches('[data-track]') ? element : element.closest('[data-track]');
                const trackData = button.getAttribute('data-track');
                trackButtonClick(trackData, {
                    elementType: button.tagName.toLowerCase(),
                    text: button.textContent.trim().substring(0, 100)
                });
            }
            
            // Track enrollment buttons
            if (element.matches('.enroll-button') || element.closest('.enroll-button')) {
                trackFeatureUsage('enrollment', { action: 'enroll_button_click' });
            }
            
            // Track user menu interactions
            if (element.matches('#user-menu-button') || element.closest('#user-menu-button')) {
                trackFeatureUsage('user_menu', { action: 'menu_open' });
            }
        });

        // Track form submissions
        document.addEventListener('submit', (event) => {
            const form = event.target;
            const formId = form.id || form.className || 'unknown_form';
            
            // Don't track immediately, wait for success/error
            setTimeout(() => {
                trackFormSubmission(formId, true);
            }, 100);
        });
    }

    // Enhanced signup tracking
    function enhanceSignupTracking() {
        // Add journey tracking to signup process
        const originalAdminData = window.signupAdminData || {};
        
        window.signupAdminData = {
            ...originalAdminData,
            userJourney: {
                firstLoginDate: null, // Will be set on first login
                signupDate: firebase.firestore.FieldValue.serverTimestamp(),
                signupSource: window.detectLeadSource ? window.detectLeadSource() : 'unknown',
                signupUserAgent: navigator.userAgent,
                totalSessions: 0,
                totalEvents: 0,
                featuresUsed: [],
                pages: [],
                events: []
            }
        };
    }

    // Public API
    window.UserJourneyTracker = {
        initialize: initializeTracking,
        trackEvent,
        trackMilestone,
        trackPageView,
        trackFeatureUsage,
        trackButtonClick,
        trackNavigation,
        trackFormSubmission,
        trackModalInteraction,
        trackSubscriptionEvent,
        trackSearchFilter,
        trackTimeOnPage,
        trackSessionEnd,
        setupAutomaticTracking,
        enhanceSignupTracking,

        // New granular tracking methods
        trackInvitationFeatureUsage,
        trackSkillsGapAnalysisUsage,
        trackAssessmentFeatureUsage,
        trackDashboardFeatureUsage,
        trackReportsFeatureUsage,
        trackLearningPathsFeatureUsage,
        updateFeatureAccess,
        updateFeatureUsage,

        // Utility methods
        setTrackingEnabled: (enabled) => { trackingEnabled = enabled; },
        getCurrentSession: () => ({ sessionId, currentPage, sessionStartTime }),

        // Updated quick tracking methods (now only track access for navigation)
        trackDashboardAccess: () => updateFeatureAccess('dashboard'),
        trackInviteAccess: () => updateFeatureAccess('invitations'),
        trackAssessmentAccess: () => updateFeatureAccess('assessments'),
        trackReportsAccess: () => updateFeatureAccess('reports'),
        trackLearningPathsAccess: () => updateFeatureAccess('learningPaths'),

        // Specific usage tracking methods
        trackDashboardUsage: (action, data) => trackDashboardFeatureUsage(action, data),
        trackInviteUsage: (action, data) => trackInvitationFeatureUsage(action, data),
        trackAssessmentUsage: (action, data) => trackAssessmentFeatureUsage(action, data),
        trackReportsUsage: (action, data) => trackReportsFeatureUsage(action, data),
        trackLearningPathsUsage: (action, data) => trackLearningPathsFeatureUsage(action, data),
        trackSkillsGapUsage: (source, data) => trackSkillsGapAnalysisUsage(source, data),

        // Modal and other tracking
        trackSubscriptionModal: (action) => trackModalInteraction('subscription_modal', action),
        trackWelcomeModal: (action) => trackModalInteraction('welcome_modal', action),
        trackTourCompletion: (status) => trackEvent('tour_completion', { status }),
        trackEmailVerification: (status) => trackEvent('email_verification', { status }),
        trackDemo: (action, feature) => trackEvent('demo_mode', { action, feature })
    };

    // Auto-initialize when Firebase auth state changes
    if (typeof firebase !== 'undefined') {
        firebase.auth().onAuthStateChanged((user) => {
            if (user && !sessionId) {
                setTimeout(initializeTracking, 1000); // Small delay to ensure page is ready
                setTimeout(setupAutomaticTracking, 1500);
            }
        });
    }

    console.log('User Journey Tracking system loaded');
})();
